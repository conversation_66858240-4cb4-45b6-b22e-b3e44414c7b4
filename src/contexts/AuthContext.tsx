import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { apiClient, isApiError } from '@/lib/api-client';
import {
  useSessionQuery,
  useSignInMutation,
  useSignUpMutation,
} from '@/hooks/useAuthApi';
import {
  AuthContextType,
  WorkspaceContextType,
  PermissionsContextType,
  UserProfile,
  Workspace,
  WorkspaceInvitation,
  SignUpData,
  AuthError,
  WorkspaceError,
  PermissionError,
  AuthErrorCode,
  WorkspaceErrorCode,
  UserRole,
  Permission,
} from '@/types';


// ============================================================================
// CONTEXT DEFINITIONS
// ============================================================================

const AuthContext = createContext<AuthContextType | undefined>(undefined);
const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);
const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

// ============================================================================
// HOOKS
// ============================================================================

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const useWorkspace = () => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

// ============================================================================
// AUTH PROVIDER
// ============================================================================

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Use React Query for session management
  const {
    data: sessionData,
    isLoading: sessionLoading,
    error: sessionError,
  } = useSessionQuery();

  // Extract user from the session data
  const user = sessionData?.user || null;

  // Local state for profile and workspace (these might come from separate endpoints)
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [error, setError] = useState<AuthError | null>(null);

  // Auth mutations
  const signInMutation = useSignInMutation();
  const signUpMutation = useSignUpMutation();

  // Helper function to create AuthError from various error types
  const createAuthError = useCallback((error: any, code?: AuthErrorCode): AuthError => {
    let errorCode = code || AuthErrorCode.UNKNOWN_ERROR;
    let message = 'An unknown error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Map common API error messages to our error codes
    if (message.includes('Invalid login credentials') || message.includes('Invalid email or password')) {
      errorCode = AuthErrorCode.INVALID_CREDENTIALS;
    } else if (message.includes('Email not confirmed') || message.includes('Email not verified')) {
      errorCode = AuthErrorCode.EMAIL_NOT_VERIFIED;
    } else if (message.includes('Password should be at least') || message.includes('weak password')) {
      errorCode = AuthErrorCode.WEAK_PASSWORD;
    } else if (message.includes('User already registered') || message.includes('Email already exists')) {
      errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
    } else if (message.includes('Signups not allowed')) {
      errorCode = AuthErrorCode.SIGNUP_DISABLED;
    } else if (message.includes('network') || message.includes('fetch')) {
      errorCode = AuthErrorCode.NETWORK_ERROR;
    }

    return {
      code: errorCode,
      message,
      timestamp: new Date().toISOString(),
    };
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Clear all auth data from memory
  const clearAuthData = useCallback(() => {
    setProfile(null);
    setWorkspace(null);
  }, []);

  // Sign in function using React Query mutation
  const signIn = useCallback(async (email: string, password: string, workspaceId?: string) => {
    try {
      setError(null);
      console.log('AuthContext: Signing in with React Query...');

      await signInMutation.mutateAsync({ email, password, rememberMe: true });

      // TODO: Handle workspace selection if workspaceId is provided
      if (workspaceId) {
        console.log('AuthContext: Workspace selection not yet implemented:', workspaceId);
      }

      console.log('AuthContext: Sign in successful');
    } catch (error) {
      console.error('AuthContext: Sign in failed:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    }
  }, [signInMutation, createAuthError]);

  // Sign up function using React Query mutation
  const signUp = useCallback(async (data: SignUpData) => {
    try {
      setError(null);
      console.log('AuthContext: Signing up with React Query...');

      await signUpMutation.mutateAsync(data);

      console.log('AuthContext: Sign up successful');
    } catch (error) {
      console.error('AuthContext: Sign up failed:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 409) {
          errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
        } else if (error.status === 400) {
          errorCode = AuthErrorCode.WEAK_PASSWORD;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    }
  }, [signUpMutation, createAuthError]);

  const queryClient = useQueryClient();

  // Sign out: immediately clear UI state and cache, then notify server
  const signOut = useCallback(async () => {
    try {
      setError(null);
      console.log('AuthContext: Signing out...');

      // Immediately clear all in-memory app state
      clearAuthData();
      queryClient.clear();
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (_) {
        // ignore
      }

      // Fire-and-forget server sign-out; do not block UI
      apiClient.signOut().catch((e) => {
        console.warn('Server sign-out failed (ignored):', e);
      });

      console.log('AuthContext: Local sign out complete');
    } catch (error) {
      console.error('AuthContext: Sign out failed:', error);
      // Ensure local cleanup even on unexpected errors
      clearAuthData();
      queryClient.clear();
      setError(createAuthError(error));
    }
  }, [clearAuthData, createAuthError, queryClient]);

  // Switch workspace
  const switchWorkspace = useCallback(async (workspaceId: string) => {
    try {
      setError(null);
      console.log('AuthContext: Switching workspace...', workspaceId);

      // TODO: Implement workspace switching with your API
      // For now, we'll just update the local workspace state
      setWorkspace(null);

      console.log('AuthContext: Workspace switch successful');
    } catch (error) {
      console.error('AuthContext: Workspace switch failed:', error);
      setError(createAuthError(error));
      throw error;
    }
  }, [createAuthError]);

  // Handle session errors and update local error state
  useEffect(() => {
    if (sessionError) {
      setError(createAuthError(sessionError, AuthErrorCode.SESSION_EXPIRED));
    } else {
      // Clear error when session is successful
      setError(null);
    }
  }, [sessionError, createAuthError]);

  // Determine loading state - combine session loading with mutation loading
  const loading = sessionLoading || signInMutation.isPending || signUpMutation.isPending;

  const value: AuthContextType = {
    user,
    profile,
    workspace,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    switchWorkspace,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// ============================================================================
// WORKSPACE PROVIDER
// ============================================================================

interface WorkspaceProviderProps {
  children: React.ReactNode;
}

const WorkspaceProvider: React.FC<WorkspaceProviderProps> = ({ children }) => {
  const { workspace: authWorkspace } = useAuth();

  // Local state for workspace data
  const [workspace, setWorkspace] = useState<Workspace | null>(authWorkspace);
  const [teamMembers, setTeamMembers] = useState<UserProfile[]>([]);
  const [invitations, setInvitations] = useState<WorkspaceInvitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<WorkspaceError | null>(null);

  // Helper function to create WorkspaceError
  const createWorkspaceError = useCallback((error: any, code?: WorkspaceErrorCode): WorkspaceError => {
    let errorCode = code || WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED;
    let message = 'A workspace error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    return {
      code: errorCode,
      message,
      workspace_id: workspace?.id,
      timestamp: new Date().toISOString(),
    };
  }, [workspace?.id]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Update workspace
  const updateWorkspace = useCallback(async (data: Partial<Workspace>) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Updating workspace...', data);

      // TODO: Implement API call to update workspace
      // const updatedWorkspace = await apiClient.updateWorkspace(workspace!.id, data);
      // setWorkspace(updatedWorkspace);

      console.log('WorkspaceContext: Workspace update successful');
    } catch (error) {
      console.error('WorkspaceContext: Workspace update failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace, createWorkspaceError]);

  // Invite team member
  const inviteTeamMember = useCallback(async (email: string, role: UserRole, redirectTo: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Inviting team member...', { email, role, redirectTo });

      // TODO: Implement API call to invite team member
      // const invitation = await apiClient.inviteTeamMember({ email, role, redirectTo });
      // setInvitations(prev => [...prev, invitation]);

      console.log('WorkspaceContext: Team member invitation sent');
    } catch (error) {
      console.error('WorkspaceContext: Team member invitation failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Remove team member
  const removeTeamMember = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Removing team member...', userId);

      // TODO: Implement API call to remove team member
      // await apiClient.removeTeamMember(userId);
      setTeamMembers(prev => prev.filter(member => member.id !== userId));

      console.log('WorkspaceContext: Team member removed');
    } catch (error) {
      console.error('WorkspaceContext: Team member removal failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Update member role
  const updateMemberRole = useCallback(async (userId: string, role: UserRole) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Updating member role...', { userId, role });

      // TODO: Implement API call to update member role
      // await apiClient.updateMemberRole(userId, role);
      setTeamMembers(prev => prev.map(member =>
        member.id === userId ? { ...member, role } : member
      ));

      console.log('WorkspaceContext: Member role updated');
    } catch (error) {
      console.error('WorkspaceContext: Member role update failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Resend invitation
  const resendInvitation = useCallback(async (invitationId: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Resending invitation...', invitationId);

      // TODO: Implement API call to resend invitation
      // await apiClient.resendInvitation(invitationId);

      console.log('WorkspaceContext: Invitation resent');
    } catch (error) {
      console.error('WorkspaceContext: Invitation resend failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Revoke invitation
  const revokeInvitation = useCallback(async (invitationId: string) => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Revoking invitation...', invitationId);

      // TODO: Implement API call to revoke invitation
      // await apiClient.revokeInvitation(invitationId);
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));

      console.log('WorkspaceContext: Invitation revoked');
    } catch (error) {
      console.error('WorkspaceContext: Invitation revocation failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Refresh team data
  const refreshTeamData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('WorkspaceContext: Refreshing team data...');

      // TODO: Implement API calls to refresh team data
      // const [members, invites] = await Promise.all([
      //   apiClient.getTeamMembers(),
      //   apiClient.getInvitations()
      // ]);
      // setTeamMembers(members);
      // setInvitations(invites);

      console.log('WorkspaceContext: Team data refreshed');
    } catch (error) {
      console.error('WorkspaceContext: Team data refresh failed:', error);
      setError(createWorkspaceError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createWorkspaceError]);

  // Sync workspace from auth context
  useEffect(() => {
    setWorkspace(authWorkspace);
  }, [authWorkspace]);

  const value: WorkspaceContextType = {
    workspace,
    teamMembers,
    invitations,
    loading,
    error,
    updateWorkspace,
    inviteTeamMember,
    removeTeamMember,
    updateMemberRole,
    resendInvitation,
    revokeInvitation,
    refreshTeamData,
    clearError,
  };

  return (
    <WorkspaceContext.Provider value={value}>
      {children}
    </WorkspaceContext.Provider>
  );
};

// ============================================================================
// PERMISSIONS PROVIDER
// ============================================================================

interface PermissionsProviderProps {
  children: React.ReactNode;
}

const PermissionsProvider: React.FC<PermissionsProviderProps> = ({ children }) => {
  const { profile } = useAuth();

  // Local state for permissions
  const [loading] = useState(false);
  const [error] = useState<PermissionError | null>(null);

  // Get user role from profile
  const userRole = profile?.role || null;

  // Calculate permissions based on role
  const permissions = useMemo((): Permission[] => {
    if (!userRole) return [];

    const rolePermissions: Record<UserRole, Permission[]> = {
      [UserRole.OWNER]: [
        // All permissions for owner
        Permission.MANAGE_WORKSPACE,
        Permission.VIEW_WORKSPACE_SETTINGS,
        Permission.MANAGE_BILLING,
        Permission.MANAGE_TEAM,
        Permission.INVITE_MEMBERS,
        Permission.REMOVE_MEMBERS,
        Permission.ASSIGN_ROLES,
        Permission.CREATE_LISTINGS,
        Permission.EDIT_LISTINGS,
        Permission.DELETE_LISTINGS,
        Permission.VIEW_LISTINGS,
        Permission.ASSIGN_LISTINGS,
        Permission.ADD_INTERNAL_NOTES,
        Permission.VIEW_INTERNAL_NOTES,
        Permission.MENTION_TEAM_MEMBERS,
        Permission.VIEW_ACTIVITY_FEED,
      ],
      [UserRole.ADMIN]: [
        // Admin permissions (all except billing and workspace management)
        Permission.VIEW_WORKSPACE_SETTINGS,
        Permission.MANAGE_TEAM,
        Permission.INVITE_MEMBERS,
        Permission.REMOVE_MEMBERS,
        Permission.ASSIGN_ROLES,
        Permission.CREATE_LISTINGS,
        Permission.EDIT_LISTINGS,
        Permission.DELETE_LISTINGS,
        Permission.VIEW_LISTINGS,
        Permission.ASSIGN_LISTINGS,
        Permission.ADD_INTERNAL_NOTES,
        Permission.VIEW_INTERNAL_NOTES,
        Permission.MENTION_TEAM_MEMBERS,
        Permission.VIEW_ACTIVITY_FEED,
      ],
      [UserRole.MEMBER]: [
        // Member permissions (basic listing and collaboration)
        Permission.CREATE_LISTINGS,
        Permission.EDIT_LISTINGS,
        Permission.VIEW_LISTINGS,
        Permission.ADD_INTERNAL_NOTES,
        Permission.VIEW_INTERNAL_NOTES,
        Permission.MENTION_TEAM_MEMBERS,
        Permission.VIEW_ACTIVITY_FEED,
      ],
      [UserRole.VIEWER]: [
        // Viewer permissions (read-only)
        Permission.VIEW_LISTINGS,
        Permission.VIEW_INTERNAL_NOTES,
        Permission.VIEW_ACTIVITY_FEED,
      ],
    };

    return rolePermissions[userRole] || [];
  }, [userRole]);

  // Permission checking functions
  const hasPermission = useCallback((permission: Permission): boolean => {
    return permissions.includes(permission);
  }, [permissions]);

  const canAccessFeature = useCallback((feature: string): boolean => {
    // Map features to permissions
    const featurePermissions: Record<string, Permission[]> = {
      'listings': [Permission.VIEW_LISTINGS],
      'create-listing': [Permission.CREATE_LISTINGS],
      'edit-listing': [Permission.EDIT_LISTINGS],
      'delete-listing': [Permission.DELETE_LISTINGS],
      'team-management': [Permission.MANAGE_TEAM],
      'workspace-settings': [Permission.VIEW_WORKSPACE_SETTINGS],
      'billing': [Permission.MANAGE_BILLING],
    };

    const requiredPermissions = featurePermissions[feature] || [];
    return requiredPermissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  const canManageTeam = useCallback((): boolean => {
    return hasPermission(Permission.MANAGE_TEAM);
  }, [hasPermission]);

  const canManageBilling = useCallback((): boolean => {
    return hasPermission(Permission.MANAGE_BILLING);
  }, [hasPermission]);

  const canEditListing = useCallback((listingId: string): boolean => {
    // TODO: Add logic for listing ownership/assignment
    console.log('Checking edit permission for listing:', listingId);
    return hasPermission(Permission.EDIT_LISTINGS);
  }, [hasPermission]);

  const canViewWorkspaceSettings = useCallback((): boolean => {
    return hasPermission(Permission.VIEW_WORKSPACE_SETTINGS);
  }, [hasPermission]);

  const canInviteMembers = useCallback((): boolean => {
    return hasPermission(Permission.INVITE_MEMBERS);
  }, [hasPermission]);

  const canRemoveMembers = useCallback((): boolean => {
    return hasPermission(Permission.REMOVE_MEMBERS);
  }, [hasPermission]);

  const canAssignRoles = useCallback((): boolean => {
    return hasPermission(Permission.ASSIGN_ROLES);
  }, [hasPermission]);

  const value: PermissionsContextType = {
    userRole,
    permissions,
    hasPermission,
    canAccessFeature,
    canManageTeam,
    canManageBilling,
    canEditListing,
    canViewWorkspaceSettings,
    canInviteMembers,
    canRemoveMembers,
    canAssignRoles,
    loading,
    error,
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// ============================================================================
// COMBINED PROVIDER
// ============================================================================

interface AuthCombinedProviderProps {
  children: React.ReactNode;
}

export const AuthCombinedProvider: React.FC<AuthCombinedProviderProps> = ({ children }) => {
  return (
    <AuthProvider>
      <WorkspaceProvider>
        <PermissionsProvider>
          {children}
        </PermissionsProvider>
      </WorkspaceProvider>
    </AuthProvider>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

// Export individual providers for flexibility
export { AuthProvider, WorkspaceProvider, PermissionsProvider };

// Export the combined provider as the default
export default AuthCombinedProvider;